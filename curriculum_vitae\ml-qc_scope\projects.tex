\begin{rubric}{项目经历}
  \subrubric{智能信贷风控模型与多源数据融合分析}
  \entry*[2022] \textbf{项目背景：}该项目旨在通过机器学习技术，精准预测客户贷款违约风险。项目核心挑战在于如何利用多维度的匿名异构数据，构建高精度的风险预测模型。
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 整合多信源的数百万条数据表（包括客户申请信息、征信局历史数据、历史贷款记录、信用卡月度账单和匿名特征等），处理包含数百个原始特征的复杂数据集。
    \item 设计并实施了多种缺失值填充策略、异常值检测与处理，以及数据类型优化，将数据处理阶段的内存占用降低了 60\%。
          % \item 将多个来源的非结构化和半结构化数据进行连接与对齐，构建了一个统一的、可供模型使用的宽表视图。
    \item 总计构建了超过500个具有强预测能力的衍生特征，使交付模型的 AUC 相比基线模型提升了 15\%。
          % \item 构建了以 LightGBM 为核心的梯度提升决策树模型，并通过贝叶斯优化进行超参数调优，实现了模型性能的最优化。
    \item 实施了模型集成策略（Ensemble Learning），融合了多个模型的预测结果，进一步提升了模型的稳定性和泛化能力，最终取得了优异的预测效果。
  \end{enumerate}
  \entry*[] \textbf{项目成果：}构建了一个可配置、可扩展且易于维护的机器学习流水线，在海量测试样本上表现出色，AUC分数达到0.8+。
  %
  %
  %
  \subrubric{量子近似优化算法在投资组合优化中的应用}
  \entry*[2022] \textbf{项目背景：}针对投资业务中普遍运用的大类资产配置所需的组合优化和风险控制要求，探索通过量子近似优化算法（QAOA）等量子算法求解离散大类资产配置问题的方法。
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 基于马科维兹模型（Markowitz Model）的现代投资组合理论进行建模，转化为一个二次无约束二元优化（QUBO）问题。实现暴力搜索解法作为base line模型。
    \item 设计并实现了QAOA的投资组合优化算法，包括量子线路的构建、参数优化以及结果解码等关键步骤。并优化QAOA算法时间效率和准确率，使其在平均近似比指标上相较经典搜索算法有提升。
    \item 实现了基于量子核的支持向量机，辅助识别影响回报或风险的关键资产特征（如波动率、相关性），从而减少维数，简化马科维兹模型的输入。
  \end{enumerate}
  \entry*[] \textbf{项目成果：}在模拟器上运行的QAOA算法通过建信基金实际业务的POC测试，使得量子算法应用于资产组合的优化和风险控制成为可能。
  %
  %
  %
  \subrubric{分钟级金融时序数据的资产价格预测系统}
  \entry*[2023] \textbf{项目背景：}为分钟级的资产市场价格预测场景，构建了一个稳健、可扩展且自动化的机器学习预测管道。核心目标在于深度挖掘数据的时间依赖性与非线性特征关系，并通过复杂的模型集成技术挑战预测性能的极限。
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 清洗高频交易数据，处理缺失值和异常值，构建标准化的时序特征工程流水线。处理超过50GB的宽表时序数据，通过数据类型优化和分块处理降低内存占用。
    \item 设计并构建了一套丰富的特征体系，包括滞后特征、滑动窗口统计特征（均值、波动率）以及指数加权移动平均 (EWMA)，有效捕捉了市场的多维度动态。
    \item 构建了混合建模框架，融合了 GBDT 模型 (LightGBM/XGBoost) 和深度学习模型 (LSTM) 的优势，分别用于捕捉数据中的截面特征关系和时间序列依赖。
    \item 采用 Stacking 集成策略，将不同基模型的预测结果作为元特征，训练一个最终的元学习器，显著提升了预测的准确性和鲁棒性。
  \end{enumerate}
  \entry*[] \textbf{项目成果：}通过精细的特征工程和高级集成策略，模型性能监控的皮尔逊相关系数提升30\%。并成功构建一个端到端的预测系统，将从接收新数据到产出预测结果的周期从数小时缩短至30分钟以内。
  %
  %
  %
  \subrubric{金融AI云平台}
  \entry*[2023] \textbf{项目背景：}金融AI云平台作为建行云的子品牌，完善建行云人工智能能力建设，打造面向金融行业的“训推一体化”人工智能技术中台，形成贴近金融业务的AI服务能力。
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 作为架构师，设计了MLaaS的特征引擎、估值引擎与风险引擎这三个应用服务，确保系统的可扩展性和高可用性。包括服务的应用架构、部署架构和安全架构。
    \item 特征引擎用于非结构化数据挖掘，部署了微调的 BERT 模型用于市场情绪分析。输出[CLS]Tokens 的嵌入向量（768 维）作为情绪因子，供下游金融模型使用。
    \item 估值引擎部署Black-Derman-Toy 利率模型和外汇远期模型，实现含权债券和外汇远期合约的精确估值，为投资组合管理提供市场价格参考，支撑投资决策。
    \item 风险引擎引入预期尾部损失 (ES) 风险指标计算，并支持多流动性期限，提供更全面的风险视图，符合巴塞尔 III 标准监管要求。
  \end{enumerate}
  \entry*[] \textbf{项目成果：}从架构设计和算法流水线的角度出发，提高了云上模型的透明化、可追溯性，并支持模型的快速迭代与更新，满足内部审计和外部监管要求。
  %
  %
  %
  \subrubric{高性能量子线路模拟器}
  \entry*[2024] \textbf{项目背景：}在参考主流模拟器及行业标准的基础上，自研量子线路模拟器应支持Python生态系统的易用性、可移植性、可维护性和通用性。本课题以主流模拟器编程语言为基础，研究并设计
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 抓取量子硬件厂商API接口，设计了量子线路模拟器的语法和功能接口，包括可视化模块与并行计算加速功能语法，以支持描述量子线路的OpenQASM脚本与GPU计算后端库。
    \item 基于量子多体数值计算方法，利用凝聚态物理的密度矩阵截断技巧，设计了矩阵乘积态（MPS）对经典多层感知机（MLP）的替代，并设计了专用优化器密度矩阵重整化群（DMRG），交付一套MPS+DMRG的量子启发式分类器框架算法。
    \item 设计实现了量子模拟并行化策略，基于pytorch张量的折叠与缩并算子，高效节约运存，结合矩阵低秩近似算法，实现超50量子比特的近似模拟。
  \end{enumerate}
  \entry*[] \textbf{项目成果：}单卡最高28量子比100层量子神经网络模型，完成一次包含6万多个量子门的推理任务，自研量子模拟器需要304秒（5分钟），而Pennylane量子模拟器需要4387秒（1.22小时），运算效率有14倍的提升。
  %
  %
  %
  \subrubric{基于大模型的文档审核智能体}
  \entry*[2025] \textbf{项目背景：}技术管理团队要对事业群五十多个团队的投产版本任务进行需求分析文档、详细设计文档、应用组装测试案例等文档，进行分级评审和质量评分。将重复性、耗时长的文档初审工作交由智能体处理，使技术管理团队能够将更多精力投入到高价值的复杂问题分析和决策中。
  \entry*[] \textbf{职责与贡献：}
  \begin{enumerate}
    \item 利用LangChain构建RAG智能体，实现文档内容完整度审核与质量打分的批处理。
    \item 利用browser-use构建浏览器智能体，开发了前端页面，实现了智能体处理结果的自动化填入功能，提升了用户体验和操作便捷性。
    \item 持续优化Prompt策略，以在保证审核质量的同时，最大限度地降低Token消耗，提升运行速度。
  \end{enumerate}
  \entry*[] \textbf{项目成果：}在管理效益上，充分详实的审查提升了各项目组的制品质量，约束了产品开发规范，降低了产品上线风险。

\end{rubric}